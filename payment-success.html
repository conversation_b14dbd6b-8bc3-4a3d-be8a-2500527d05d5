<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Оплата успішна - Admins.Today</title>
    <meta name="description" content="Ваша оплата успішно оброблена. Наші адміністратори зв'яжуться з вами найближчим часом.">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">
    
    <style>
        .success-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            background: linear-gradient(135deg, 
                rgba(99, 102, 241, 0.1) 0%, 
                rgba(236, 72, 153, 0.1) 100%);
        }
        
        .success-container {
            max-width: 600px;
            width: 100%;
            background: var(--bg-secondary);
            border-radius: var(--border-radius-lg);
            padding: 3rem;
            text-align: center;
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--border-color);
        }
        
        .success-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            background: linear-gradient(135deg, var(--success-color), #10b981);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            animation: successPulse 2s ease-in-out infinite;
            box-shadow: 0 0 30px rgba(34, 197, 94, 0.3);
        }
        
        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .success-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--success-color), #10b981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .success-message {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .success-details {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: left;
        }
        
        .success-details h4 {
            color: var(--success-color);
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .success-details ul {
            list-style: none;
            padding: 0;
        }
        
        .success-details li {
            padding: 0.5rem 0;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .success-details li i {
            color: var(--success-color);
            width: 20px;
        }
        
        .success-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .order-info {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: left;
        }
        
        .order-info h4 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .order-info p {
            margin: 0.5rem 0;
            color: var(--text-secondary);
        }
        
        .order-info strong {
            color: var(--text-primary);
        }
        
        @media (max-width: 768px) {
            .success-container {
                padding: 2rem 1.5rem;
            }
            
            .success-title {
                font-size: 2rem;
            }
            
            .success-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-logo">
                <div class="nav-futuristic-icon">
                    <div class="nav-icon-core">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div class="nav-icon-rings">
                        <div class="nav-ring nav-ring-1"></div>
                        <div class="nav-ring nav-ring-2"></div>
                    </div>
                </div>
                <span>Admins.Today</span>
            </div>
            <div class="nav-links">
                <a href="/">Головна</a>
                <a href="/#services">Послуги</a>
                <a href="/contacts">Контакти</a>
            </div>
        </div>
    </nav>

    <!-- Success Page -->
    <main class="success-page">
        <div class="success-container">
            <div class="success-icon">
                <i class="fas fa-check"></i>
            </div>
            
            <h1 class="success-title">Оплата успішна!</h1>
            
            <p class="success-message">
                Дякуємо за довіру! Ваша оплата успішно оброблена, і ваше замовлення прийнято в роботу.
            </p>
            
            <div class="order-info" id="orderInfo">
                <h4><i class="fas fa-receipt"></i> Деталі замовлення</h4>
                <p><strong>Номер замовлення:</strong> <span id="orderNumber">Завантаження...</span></p>
                <p><strong>Сума оплати:</strong> <span id="orderAmount">Завантаження...</span></p>
                <p><strong>Тариф:</strong> <span id="orderTariff">Завантаження...</span></p>
                <p><strong>Дата:</strong> <span id="orderDate">Завантаження...</span></p>
            </div>
            
            <div class="success-details">
                <h4><i class="fas fa-clock"></i> Що далі?</h4>
                <ul>
                    <li><i class="fas fa-phone"></i> Наш менеджер зв'яжеться з вами протягом 15 хвилин</li>
                    <li><i class="fas fa-cogs"></i> Почнемо роботу над вашим завданням сьогодні</li>
                    <li><i class="fas fa-envelope"></i> Відправимо деталі на вашу електронну пошту</li>
                    <li><i class="fas fa-headset"></i> Надамо доступ до особистого кабінету</li>
                </ul>
            </div>
            
            <div class="success-actions">
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    На головну
                </a>
                <a href="/contacts" class="btn btn-secondary">
                    <i class="fas fa-phone"></i>
                    Зв'язатися з нами
                </a>
                <a href="https://t.me/admins_today" class="btn btn-outline">
                    <i class="fab fa-telegram"></i>
                    Telegram
                </a>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <div class="footer-futuristic-icon">
                            <div class="footer-icon-core">
                                <i class="fas fa-microchip"></i>
                            </div>
                            <div class="footer-icon-rings">
                                <div class="footer-ring footer-ring-1"></div>
                                <div class="footer-ring footer-ring-2"></div>
                            </div>
                        </div>
                        <span>Admins.Today</span>
                    </div>
                    <p>Професійні системні адміністратори та техпідтримка для вашого бізнесу</p>
                </div>
                <div class="footer-section">
                    <h4>Контакти</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>

                        <p><i class="fas fa-clock"></i> 24/7</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Admins.Today. Усі права захищені.</p>
            </div>
        </div>
    </footer>

    <script>
        // Получаем параметры из URL
        const urlParams = new URLSearchParams(window.location.search);
        
        // Заполняем информацию о заказе
        document.getElementById('orderNumber').textContent = 
            urlParams.get('orderReference') || 'Не вказано';
        
        document.getElementById('orderAmount').textContent = 
            urlParams.get('amount') ? `$${urlParams.get('amount')} ${urlParams.get('currency') || 'USD'}` : 'Не вказано';
        
        document.getElementById('orderTariff').textContent = 
            urlParams.get('productName') || 'Не вказано';
        
        document.getElementById('orderDate').textContent = 
            new Date().toLocaleString('uk-UA');
        
        // Отправляем событие в Google Analytics / Facebook Pixel
        if (typeof gtag !== 'undefined') {
            gtag('event', 'purchase', {
                'transaction_id': urlParams.get('orderReference'),
                'value': parseFloat(urlParams.get('amount')) || 0,
                'currency': urlParams.get('currency') || 'USD'
            });
        }
        
        if (typeof fbq !== 'undefined') {
            fbq('track', 'Purchase', {
                value: parseFloat(urlParams.get('amount')) || 0,
                currency: urlParams.get('currency') || 'USD'
            });
        }
        
        console.log('✅ Payment success page loaded');
    </script>
</body>
</html>
