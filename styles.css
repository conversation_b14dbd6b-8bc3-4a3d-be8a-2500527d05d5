/* CSS Variables */
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #ec4899;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-card: rgba(255, 255, 255, 0.05);
    --bg-glass: rgba(255, 255, 255, 0.1);
    
    --text-primary: #ffffff;
    --text-secondary: #a1a1aa;
    --text-muted: #71717a;
    
    --border-color: rgba(255, 255, 255, 0.1);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-secondary: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    --gradient-bg: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
    
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--gradient-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 16px;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--bg-glass);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--bg-card);
    transform: translateY(-2px);
}

.btn-full {
    width: 100%;
    justify-content: center;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 15, 35, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition);
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
}

/* НОВЫЙ ЛОГОТИП В НАВИГАЦИИ */
.nav-logo-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    overflow: hidden;
}

.nav-logo-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.nav-logo-icon:hover img {
    transform: scale(1.1);
}

/* Стили для футера */
.footer-logo-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    overflow: hidden;
}

.footer-logo-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

.footer-logo-icon:hover img {
    opacity: 1;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition);
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 100px 0 50px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 2;
}

/* Animated Background */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

/* Network Grid */
.network-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 40s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* Floating Servers */
.server-node {
    position: absolute;
    width: 60px;
    height: 40px;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(236, 72, 153, 0.2));
    border: 2px solid rgba(99, 102, 241, 0.3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.2rem;
    animation: serverFloat 12s ease-in-out infinite;
    backdrop-filter: blur(10px);
}

.server-node::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    width: 4px;
    height: 4px;
    background: var(--success-color);
    border-radius: 50%;
    animation: serverBlink 4s ease-in-out infinite;
}

.server-node::after {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 4px;
    height: 4px;
    background: var(--accent-color);
    border-radius: 50%;
    animation: serverBlink 4s ease-in-out infinite 1s;
}

@keyframes serverFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(1deg); }
    50% { transform: translateY(-5px) rotate(0deg); }
    75% { transform: translateY(-15px) rotate(-1deg); }
}

@keyframes serverBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* Data Flow Lines */
.data-flow {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    animation: dataMove 6s linear infinite;
}

@keyframes dataMove {
    0% { transform: translateX(-100%); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateX(100vw); opacity: 0; }
}

/* Code Particles */
.code-particle {
    position: absolute;
    color: var(--accent-color);
    font-family: 'Courier New', monospace;
    font-size: 12px;
    opacity: 0.7;
    animation: codeFloat 16s linear infinite;
    pointer-events: none;
}

@keyframes codeFloat {
    0% {
        transform: translateY(100vh) translateX(0px);
        opacity: 0;
    }
    10% { opacity: 0.7; }
    90% { opacity: 0.7; }
    100% {
        transform: translateY(-100px) translateX(50px);
        opacity: 0;
    }
}

/* Network Connections */
.network-line {
    position: absolute;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(99, 102, 241, 0.5),
        rgba(236, 72, 153, 0.5),
        transparent
    );
    animation: networkPulse 8s ease-in-out infinite;
}

@keyframes networkPulse {
    0%, 100% { opacity: 0.3; transform: scaleX(1); }
    50% { opacity: 1; transform: scaleX(1.1); }
}

/* Terminal Windows */
.terminal-window {
    position: absolute;
    width: 200px;
    height: 120px;
    background: rgba(15, 15, 35, 0.9);
    border: 1px solid var(--primary-color);
    border-radius: 8px;
    padding: 20px 10px 10px;
    font-family: 'Courier New', monospace;
    font-size: 10px;
    color: var(--success-color);
    animation: terminalFloat 10s ease-in-out infinite;
    backdrop-filter: blur(10px);
}

.terminal-window::before {
    content: '';
    position: absolute;
    top: 6px;
    left: 8px;
    width: 8px;
    height: 8px;
    background: var(--error-color);
    border-radius: 50%;
    box-shadow:
        12px 0 0 var(--warning-color),
        24px 0 0 var(--success-color);
}

.terminal-content {
    line-height: 1.2;
    animation: typeWriter 3s steps(40) infinite;
}

@keyframes terminalFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(0.5deg); }
    66% { transform: translateY(-10px) rotate(-0.5deg); }
}

@keyframes typeWriter {
    0% { width: 0; }
    50% { width: 100%; }
    100% { width: 0; }
}

/* CPU Load Bars */
.cpu-load {
    position: absolute;
    width: 100px;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.cpu-load::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--warning-color), var(--error-color));
    animation: cpuUsage 2s ease-in-out infinite;
}

@keyframes cpuUsage {
    0% { width: 20%; }
    25% { width: 60%; }
    50% { width: 85%; }
    75% { width: 45%; }
    100% { width: 20%; }
}

/* Memory Usage Circles */
.memory-circle {
    position: absolute;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(99, 102, 241, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: var(--primary-color);
    animation: memoryPulse 3s ease-in-out infinite;
}

.memory-circle::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 3px solid transparent;
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: memoryRotate 2s linear infinite;
}

@keyframes memoryPulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.1); opacity: 1; }
}

@keyframes memoryRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Network Traffic Indicators */
.traffic-indicator {
    position: absolute;
    width: 20px;
    height: 20px;
    background: var(--accent-color);
    border-radius: 50%;
    animation: trafficBlink 1.5s ease-in-out infinite;
}

@keyframes trafficBlink {
    0%, 100% { opacity: 0.3; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.2); }
}

/* Floating Icons */
.floating-icon {
    position: absolute;
    font-size: 1.5rem;
    color: rgba(99, 102, 241, 0.6);
    animation: iconFloat 8s ease-in-out infinite;
    pointer-events: none;
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) rotate(90deg);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-10px) rotate(180deg);
        opacity: 1;
    }
    75% {
        transform: translateY(-30px) rotate(270deg);
        opacity: 0.8;
    }
}

/* Binary Rain Effect */
.binary-rain {
    position: absolute;
    top: -100px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: rgba(6, 182, 212, 0.4);
    animation: binaryFall 6s linear infinite;
    pointer-events: none;
}

@keyframes binaryFall {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

/* Status Lights */
.status-light {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: statusBlink 2s ease-in-out infinite;
}

.status-light.green {
    background: var(--success-color);
    box-shadow: 0 0 10px var(--success-color);
}

.status-light.yellow {
    background: var(--warning-color);
    box-shadow: 0 0 10px var(--warning-color);
    animation-delay: 0.5s;
}

.status-light.red {
    background: var(--error-color);
    box-shadow: 0 0 10px var(--error-color);
    animation-delay: 1s;
}

@keyframes statusBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* Performance Optimizations */
.hero-background * {
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Additional Visual Effects */
.hero-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.05) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(236, 72, 153, 0.05) 0%, transparent 40%),
        radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.03) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
    pointer-events: none;
}

@keyframes backgroundShift {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.5;
    }
    33% {
        transform: scale(1.1) rotate(1deg);
        opacity: 0.7;
    }
    66% {
        transform: scale(0.9) rotate(-1deg);
        opacity: 0.6;
    }
}

/* Glitch Effect for Terminal */
.terminal-window.glitch {
    animation: terminalGlitch 0.3s ease-in-out;
}

@keyframes terminalGlitch {
    0% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
    100% { transform: translate(0); }
}

/* Holographic Effect */
.server-node.hologram {
    background: linear-gradient(45deg,
        rgba(99, 102, 241, 0.1),
        rgba(236, 72, 153, 0.1),
        rgba(6, 182, 212, 0.1)
    );
    background-size: 200% 200%;
    animation: hologramShift 3s ease-in-out infinite, serverFloat 6s ease-in-out infinite;
}

@keyframes hologramShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Matrix-style Code Rain */
.matrix-rain {
    position: absolute;
    top: -100px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: rgba(0, 255, 0, 0.6);
    animation: matrixFall 8s linear infinite;
    pointer-events: none;
    text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

@keyframes matrixFall {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

/* Scanning Line Effect */
.scan-line {
    position: absolute;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg,
        transparent,
        rgba(0, 255, 0, 0.8),
        transparent
    );
    animation: scanMove 4s linear infinite;
    pointer-events: none;
}

@keyframes scanMove {
    0% { top: 0%; opacity: 1; }
    100% { top: 100%; opacity: 0; }
}

/* Advantages Section */
.advantages-section {
    padding: 100px 0;
    background: var(--bg-tertiary);
    position: relative;
    overflow: hidden;
}

.advantages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.advantage-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 30px;
    text-align: center;
    transition: var(--transition);
}

.advantage-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.advantage-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.advantage-card h4 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.advantage-card p {
    color: var(--text-secondary);
    line-height: 1.5;
}

/* Стили для карточек преимуществ в отдельной секции */

/* Удаляем неиспользуемые анимации - они определены ниже */

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 3;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.1;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 30px;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 500px;
}

/* Tech Visualization */
.tech-visualization {
    position: relative;
    width: 400px;
    height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Hologram Display */
.hologram-display {
    position: relative;
    width: 200px;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hologram-ring {
    position: absolute;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: hologramRotate 8s linear infinite;
}

.hologram-ring:nth-child(1) {
    width: 120px;
    height: 120px;
    border-color: var(--primary-color);
    animation-delay: 0s;
    opacity: 0.8;
}

.hologram-ring:nth-child(2) {
    width: 160px;
    height: 160px;
    border-color: var(--secondary-color);
    animation-delay: -2s;
    opacity: 0.6;
}

.hologram-ring:nth-child(3) {
    width: 200px;
    height: 200px;
    border-color: var(--accent-color);
    animation-delay: -4s;
    opacity: 0.4;
}

.central-logo {
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ОХУИТЕЛЬНЫЙ ФУТУРИСТИЧЕСКИЙ ЗНАЧОК ИЗ БУДУЩЕГО */
.futuristic-icon {
    position: relative;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-core {
    position: relative;
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #00f5ff, #ff00ff, #00ff00, #ffff00);
    background-size: 400% 400%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #000;
    animation: coreRotate 4s linear infinite, corePulse 2s ease-in-out infinite alternate, gradientShift 3s ease-in-out infinite;
    box-shadow:
        0 0 20px rgba(0, 245, 255, 0.8),
        0 0 40px rgba(255, 0, 255, 0.6),
        0 0 60px rgba(0, 255, 0, 0.4),
        0 0 80px rgba(255, 255, 0, 0.3),
        inset 0 0 20px rgba(255, 255, 255, 0.2);
    z-index: 5;
    overflow: hidden;
}

/* 💊 ПИЛЮЛЯ ДЛЯ СЕРВЕРОВ - логотип в голограмме */
.hologram-logo {
    width: 45px !important;
    height: 45px !important;
    object-fit: contain;
    /* Убираем фильтры, чтобы показать настоящую пилюлю */
    filter: drop-shadow(0 0 15px rgba(138, 92, 246, 0.8)) drop-shadow(0 0 25px rgba(168, 85, 247, 0.6));
    animation: logoFloat 3s ease-in-out infinite, logoGlow 2s ease-in-out infinite alternate;
    z-index: 1002;
    transition: all 0.3s ease;
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px) scale(1) rotate(0deg);
    }
    50% {
        transform: translateY(-3px) scale(1.05) rotate(3deg);
    }
}

@keyframes logoGlow {
    0% {
        filter: drop-shadow(0 0 15px rgba(138, 92, 246, 0.8)) drop-shadow(0 0 25px rgba(168, 85, 247, 0.6));
    }
    100% {
        filter: drop-shadow(0 0 25px rgba(138, 92, 246, 1)) drop-shadow(0 0 35px rgba(168, 85, 247, 0.8)) drop-shadow(0 0 45px rgba(124, 58, 237, 0.6));
    }
}

.icon-rings {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.ring {
    position: absolute;
    border: 2px solid;
    border-radius: 50%;
    animation: ringRotate 6s linear infinite;
}

.ring-1 {
    width: 80px;
    height: 80px;
    top: 20px;
    left: 20px;
    border-color: rgba(0, 245, 255, 0.8);
    animation-duration: 3s;
    animation-direction: normal;
    box-shadow: 0 0 15px rgba(0, 245, 255, 0.5);
}

.ring-2 {
    width: 100px;
    height: 100px;
    top: 10px;
    left: 10px;
    border-color: rgba(255, 0, 255, 0.6);
    animation-duration: 4s;
    animation-direction: reverse;
    box-shadow: 0 0 15px rgba(255, 0, 255, 0.4);
}

.ring-3 {
    width: 120px;
    height: 120px;
    top: 0;
    left: 0;
    border-color: rgba(0, 255, 0, 0.4);
    animation-duration: 5s;
    animation-direction: normal;
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
}

.icon-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.icon-particles .particle {
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    animation: particleOrbit 4s ease-in-out infinite;
    box-shadow: 0 0 15px currentColor;
}

.icon-particles .particle.p1 {
    top: 10px;
    left: 50%;
    background: #00f5ff;
    animation-delay: 0s;
}

.icon-particles .particle.p2 {
    top: 50%;
    right: 10px;
    background: #ff00ff;
    animation-delay: 1s;
}

.icon-particles .particle.p3 {
    bottom: 10px;
    left: 50%;
    background: #00ff00;
    animation-delay: 2s;
}

.icon-particles .particle.p4 {
    top: 50%;
    left: 10px;
    background: #ffff00;
    animation-delay: 3s;
}

@keyframes hologramRotate {
    0% { transform: rotate(0deg) scale(1); opacity: 0.8; }
    50% { transform: rotate(180deg) scale(1.1); opacity: 0.4; }
    100% { transform: rotate(360deg) scale(1); opacity: 0.8; }
}

/* АНИМАЦИИ ДЛЯ ОХУИТЕЛЬНОГО ЗНАЧКА */
@keyframes coreRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes corePulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.1); }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes ringRotate {
    0% { transform: rotate(0deg) scale(1); opacity: 0.8; }
    50% { transform: rotate(180deg) scale(1.05); opacity: 0.4; }
    100% { transform: rotate(360deg) scale(1); opacity: 0.8; }
}

@keyframes particleOrbit {
    0% {
        transform: rotate(0deg) translateX(40px) rotate(0deg) scale(1);
        opacity: 0.8;
    }
    25% {
        transform: rotate(90deg) translateX(45px) rotate(-90deg) scale(1.2);
        opacity: 1;
    }
    50% {
        transform: rotate(180deg) translateX(40px) rotate(-180deg) scale(0.8);
        opacity: 0.6;
    }
    75% {
        transform: rotate(270deg) translateX(45px) rotate(-270deg) scale(1.1);
        opacity: 0.9;
    }
    100% {
        transform: rotate(360deg) translateX(40px) rotate(-360deg) scale(1);
        opacity: 0.8;
    }
}

/* АНИМАЦИИ ДЛЯ ЛОГОТИПОВ */
@keyframes navCoreRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes navGradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes navRingRotate {
    0% { transform: rotate(0deg) scale(1); opacity: 0.6; }
    50% { transform: rotate(180deg) scale(1.02); opacity: 0.3; }
    100% { transform: rotate(360deg) scale(1); opacity: 0.6; }
}

@keyframes footerCoreRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(-360deg); }
}

@keyframes footerGradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes footerRingRotate {
    0% { transform: rotate(0deg) scale(1); opacity: 0.5; }
    50% { transform: rotate(180deg) scale(1.01); opacity: 0.2; }
    100% { transform: rotate(360deg) scale(1); opacity: 0.5; }
}

/* Data Streams */
.data-streams {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.stream {
    position: absolute;
    width: 2px;
    height: 100px;
    background: linear-gradient(to bottom, var(--accent-color), transparent);
    animation: streamFlow 3s ease-in-out infinite;
}

.stream-1 {
    top: 10%;
    left: 20%;
    animation-delay: 0s;
    transform: rotate(45deg);
}

.stream-2 {
    top: 20%;
    right: 15%;
    animation-delay: 0.5s;
    transform: rotate(-30deg);
}

.stream-3 {
    bottom: 25%;
    left: 15%;
    animation-delay: 1s;
    transform: rotate(60deg);
}

.stream-4 {
    bottom: 15%;
    right: 20%;
    animation-delay: 1.5s;
    transform: rotate(-45deg);
}

@keyframes streamFlow {
    0%, 100% { opacity: 0; transform: translateY(0px) rotate(var(--rotation, 0deg)); }
    50% { opacity: 1; transform: translateY(-20px) rotate(var(--rotation, 0deg)); }
}

/* Tech Particles */
.tech-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--success-color);
    border-radius: 50%;
    animation: particleFloat 6s ease-in-out infinite;
    box-shadow: 0 0 10px var(--success-color);
}

.particle:nth-child(1) {
    top: 15%;
    left: 10%;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    top: 25%;
    right: 12%;
    animation-delay: 1s;
}

.particle:nth-child(3) {
    bottom: 30%;
    left: 8%;
    animation-delay: 2s;
}

.particle:nth-child(4) {
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
}

.particle:nth-child(5) {
    top: 50%;
    left: 5%;
    animation-delay: 4s;
}

.particle:nth-child(6) {
    top: 60%;
    right: 8%;
    animation-delay: 5s;
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
        opacity: 0.3;
    }
    25% {
        transform: translateY(-30px) translateX(20px);
        opacity: 1;
    }
    50% {
        transform: translateY(-15px) translateX(-15px);
        opacity: 0.7;
    }
    75% {
        transform: translateY(-40px) translateX(10px);
        opacity: 0.9;
    }
}

/* Original Advantages Section */
.advantages-section-old {
    padding: 100px 0;
    background: var(--bg-secondary);
}

.advantages-grid-old {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

/* Дублирующиеся стили удалены - используются стили из .advantages-section .floating-card */

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-15px) rotate(2deg); }
    50% { transform: translateY(-25px) rotate(0deg); }
    75% { transform: translateY(-10px) rotate(-2deg); }
}

/* Удалены анимации плавающих карточек */

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 16px;
}

.section-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Services Section */
.services {
    padding: 100px 0;
    position: relative;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.service-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 40px 30px;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.service-card p {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

.service-features {
    list-style: none;
    text-align: left;
}

.service-features li {
    padding: 8px 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: 20px;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

.service-card .btn {
    margin-top: 20px;
    width: 100%;
}

/* Order Section */
.order-section {
    padding: 100px 0;
    background: var(--bg-secondary);
}

.order-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: start;
}

.order-form {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 40px;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 16px;
    transition: var(--transition);
}

/* Специальные стили для селекторов */
.form-group select {
    background-color: var(--bg-card) !important;
    color: var(--text-primary) !important;
    cursor: pointer;
}

/* Стили для опций в селекторе */
.form-group select option {
    background-color: var(--bg-card) !important;
    color: var(--text-primary) !important;
    padding: 8px 12px;
}

/* Для светлой темы - принудительно темный текст */
@media (prefers-color-scheme: light) {
    .form-group select {
        background-color: #ffffff !important;
        color: #1a1a1a !important;
        border-color: #e5e7eb !important;
    }

    .form-group select option {
        background-color: #ffffff !important;
        color: #1a1a1a !important;
    }
}

/* Для темной темы - принудительно светлый текст */
@media (prefers-color-scheme: dark) {
    .form-group select {
        background-color: #1f2937 !important;
        color: #f9fafb !important;
        border-color: #374151 !important;
    }

    .form-group select option {
        background-color: #1f2937 !important;
        color: #f9fafb !important;
    }
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Дополнительные стили для кроссбраузерности */
select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px !important;
}

/* Принудительные стили для всех селекторов на странице */
select,
select option {
    background-color: #ffffff !important;
    color: #1a1a1a !important;
}

/* Для пользователей с темной темой системы */
@media (prefers-color-scheme: dark) {
    select,
    select option {
        background-color: #1f2937 !important;
        color: #f9fafb !important;
        border-color: #374151 !important;
    }
}

/* Стили для конкретных селекторов формы заказа */
#service,
#urgency,
#tariff {
    background-color: #ffffff !important;
    color: #1a1a1a !important;
    border: 1px solid #d1d5db !important;
}

#service option,
#urgency option,
#tariff option {
    background-color: #ffffff !important;
    color: #1a1a1a !important;
    padding: 8px 12px;
}

/* Для темной темы */
@media (prefers-color-scheme: dark) {
    #service,
    #urgency,
    #tariff {
        background-color: #1f2937 !important;
        color: #f9fafb !important;
        border-color: #374151 !important;
    }

    #service option,
    #urgency option,
    #tariff option {
        background-color: #1f2937 !important;
        color: #f9fafb !important;
    }
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.order-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.info-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 24px;
    text-align: center;
}

.info-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 12px;
}

.info-card h4 {
    margin-bottom: 8px;
}

.info-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Chatbot */
.chatbot {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    overflow: hidden;
}

.chatbot-header {
    background: var(--gradient-primary);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: 500;
}

.chatbot-header i:last-child {
    margin-left: auto;
    transition: var(--transition);
}

.chatbot.collapsed .chatbot-header i:last-child {
    transform: rotate(180deg);
}

.chatbot-body {
    height: 400px;
    display: flex;
    flex-direction: column;
    transition: var(--transition);
}

.chatbot.collapsed .chatbot-body {
    height: 0;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.message {
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.message.user-message {
    flex-direction: row-reverse;
}

.message i {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.bot-message i {
    background: var(--gradient-primary);
    color: white;
}

.user-message i {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.message-content {
    background: var(--bg-card);
    padding: 12px 16px;
    border-radius: var(--border-radius);
    max-width: 80%;
    font-size: 14px;
    line-height: 1.4;
}

.user-message .message-content {
    background: var(--gradient-primary);
    color: white;
}

.chat-input {
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 10px;
}

.chat-input input {
    flex: 1;
    padding: 10px 12px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 14px;
}

.chat-input input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.chat-input button {
    padding: 10px 12px;
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--border-radius);
    color: white;
    cursor: pointer;
    transition: var(--transition);
}

.chat-input button:hover {
    transform: scale(1.05);
}

/* Footer */
.footer {
    background: var(--bg-primary);
    padding: 60px 0 20px;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 16px;
}

/* ОХУИТЕЛЬНЫЙ ЛОГОТИП В ФУТЕРЕ */
.footer-futuristic-icon {
    position: relative;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.footer-icon-core {
    position: relative;
    width: 28px;
    height: 28px;
    background: linear-gradient(45deg, #00f5ff, #ff00ff, #00ff00);
    background-size: 200% 200%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    color: #000;
    animation: footerCoreRotate 8s linear infinite, footerGradientShift 5s ease-in-out infinite;
    box-shadow:
        0 0 8px rgba(0, 245, 255, 0.5),
        0 0 16px rgba(255, 0, 255, 0.3),
        0 0 24px rgba(0, 255, 0, 0.2);
    z-index: 5;
}

.footer-icon-rings {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.footer-ring {
    position: absolute;
    border: 1px solid;
    border-radius: 50%;
    animation: footerRingRotate 10s linear infinite;
}

.footer-ring-1 {
    width: 32px;
    height: 32px;
    top: 2px;
    left: 2px;
    border-color: rgba(0, 245, 255, 0.5);
    animation-duration: 5s;
    animation-direction: normal;
}

.footer-ring-2 {
    width: 36px;
    height: 36px;
    top: 0;
    left: 0;
    border-color: rgba(255, 0, 255, 0.3);
    animation-duration: 7s;
    animation-direction: reverse;
}

.footer-section h4 {
    margin-bottom: 16px;
    color: var(--text-primary);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    color: var(--text-secondary);
}

.contact-info i {
    color: var(--primary-color);
    width: 16px;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* Service Pages Styles */
.service-hero {
    padding: 120px 0 80px;
    background: var(--gradient-primary);
    color: white;
    text-align: center;
}

.service-hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.service-icon {
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    font-size: 3rem;
}

.service-title {
    font-size: 3rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.service-subtitle {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.service-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-top: 50px;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.8;
}

.service-details {
    padding: 100px 0;
}

.details-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: start;
}

.details-content h2 {
    font-size: 2.5rem;
    margin-bottom: 40px;
    color: var(--text-primary);
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.feature-item {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.feature-item i {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.feature-item h4 {
    font-size: 1.3rem;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.feature-item p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.pricing-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    position: sticky;
    top: 100px;
}

.pricing-card h3 {
    font-size: 1.5rem;
    margin-bottom: 25px;
    color: var(--text-primary);
    text-align: center;
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.price-item:last-of-type {
    border-bottom: none;
    margin-bottom: 25px;
}

.price-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.price-value {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.service-process {
    padding: 100px 0;
    background: var(--bg-secondary);
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.process-step {
    text-align: center;
    padding: 30px 20px;
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.process-step h4 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.process-step p {
    color: var(--text-secondary);
    line-height: 1.5;
}

.service-technologies {
    padding: 100px 0;
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.tech-item {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 30px 20px;
    text-align: center;
    transition: var(--transition);
}

.tech-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.tech-item i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.tech-item span {
    font-weight: 500;
    color: var(--text-primary);
}

.service-cta {
    padding: 100px 0;
    background: var(--gradient-primary);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.cta-buttons .btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
}

.cta-buttons .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Cloud Platforms */
.cloud-platforms {
    padding: 100px 0;
    background: var(--bg-secondary);
}

.platforms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.platform-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 40px 30px;
    text-align: center;
    transition: var(--transition);
}

.platform-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.platform-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2.5rem;
    color: white;
}

.platform-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.platform-card p {
    color: var(--text-secondary);
    margin-bottom: 20px;
    line-height: 1.5;
}

.platform-card ul {
    list-style: none;
    text-align: left;
}

.platform-card li {
    padding: 5px 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: 20px;
}

.platform-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

/* Security Threats */
.security-threats {
    padding: 100px 0;
    background: var(--bg-secondary);
}

.threats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.threat-item {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 30px;
    text-align: center;
    transition: var(--transition);
}

.threat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.threat-item i {
    font-size: 2.5rem;
    color: var(--error-color);
    margin-bottom: 20px;
}

.threat-item h4 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.threat-item p {
    color: var(--text-secondary);
    line-height: 1.5;
}

/* Стили для оплаты */
.price-info {
    background: linear-gradient(135deg, #00f5ff, #ff00ff);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    margin-top: 10px;
}

.price-label {
    font-size: 14px;
    color: #000;
    font-weight: 500;
}

.price-amount {
    font-size: 24px;
    font-weight: 700;
    color: #000;
    margin-left: 10px;
}

.payment-info {
    margin-top: 15px;
    text-align: center;
}

.payment-info p {
    margin: 5px 0;
    font-size: 14px;
    color: var(--text-secondary);
}

.payment-info i {
    color: var(--primary-color);
    margin-right: 8px;
}

#payButton:disabled {
    background: #666;
    cursor: not-allowed;
    opacity: 0.6;
}

#payButton:not(:disabled) {
    background: linear-gradient(135deg, #00f5ff, #ff00ff);
    animation: paymentPulse 2s ease-in-out infinite;
}

@keyframes paymentPulse {
    0%, 100% {
        box-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 0, 255, 0.8);
        transform: scale(1.02);
    }
}

/* Стили для юридической информации */
.legal-info {
    margin-top: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.legal-info p {
    margin: 5px 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.legal-info strong {
    color: var(--primary-color);
}

/* Стили для юридических страниц */
.legal-page {
    min-height: 100vh;
    background: var(--bg-primary);
    padding: 120px 0 60px;
}

.legal-content {
    max-width: 800px;
    margin: 0 auto;
    background: var(--bg-secondary);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

.legal-content h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 30px;
    text-align: center;
}

.legal-content h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
    margin: 30px 0 15px;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.legal-content h3 {
    color: var(--text-primary);
    font-size: 1.2rem;
    margin: 20px 0 10px;
}

.legal-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 15px;
}

.legal-content ul, .legal-content ol {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 15px;
    padding-left: 20px;
}

.legal-content li {
    margin-bottom: 8px;
}

.legal-info-header {
    background: linear-gradient(135deg, rgba(0, 245, 255, 0.1), rgba(255, 0, 255, 0.1));
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    border: 1px solid rgba(0, 245, 255, 0.3);
}

.legal-info-header p {
    margin: 5px 0;
    color: var(--text-primary);
}

.legal-info-header strong {
    color: var(--primary-color);
}

/* Стили для страницы контактов */
.contacts-page {
    min-height: 100vh;
    background: var(--bg-primary);
    padding: 120px 0 60px;
}

.contacts-content {
    max-width: 1200px;
    margin: 0 auto;
}

.contacts-content h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 40px;
    text-align: center;
}

.contacts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.contact-card {
    background: var(--bg-secondary);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    text-align: center;
    border: 1px solid rgba(0, 245, 255, 0.2);
    transition: all 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(0, 245, 255, 0.3);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: #000;
}

.contact-card h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    margin-bottom: 20px;
}

.contact-details p {
    color: var(--text-secondary);
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.contact-details i {
    color: var(--primary-color);
    width: 20px;
}

.legal-section {
    margin: 60px 0;
}

.legal-section h2 {
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 30px;
    text-align: center;
}

.legal-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.legal-card {
    background: var(--bg-secondary);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.legal-card h3 {
    color: var(--text-primary);
    font-size: 1.2rem;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.legal-card h3 i {
    color: var(--primary-color);
}

.legal-details p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 8px 0;
}

.legal-details strong {
    color: var(--text-primary);
}

.additional-info {
    margin-top: 60px;
}

.additional-info h2 {
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 30px;
    text-align: center;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.info-item {
    background: var(--bg-secondary);
    padding: 25px;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.info-item h4 {
    color: var(--text-primary);
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.info-item p {
    color: var(--text-secondary);
    line-height: 1.5;
}

/* Navigation links */
.nav-links {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-links a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

/* Mobile navigation */
.nav-links.active {
    display: flex !important;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-secondary);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
}

.nav-links.active a {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-links.active a:last-child {
    border-bottom: none;
}

/* Hamburger menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: all 0.3s ease;
    border-radius: 2px;
}

.hamburger:hover span {
    background: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .hamburger {
        display: flex;
    }
    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 40px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .order-container {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .chatbot {
        width: calc(100vw - 40px);
        right: 20px;
        left: 20px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    /* Service Pages Mobile */
    .service-title {
        font-size: 2rem;
    }

    .service-stats {
        flex-direction: column;
        gap: 30px;
    }

    .details-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .pricing-card {
        position: static;
    }

    .process-steps {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .tech-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
    }

    .platforms-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .threats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}


