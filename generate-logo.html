<!DOCTYPE html>
<html>
<head>
    <title>Generate Logo</title>
    <style>
        body { background: #000; margin: 0; padding: 20px; }
        .logo-static, .logo-animated { 
            width: 200px; 
            height: 200px; 
            margin: 20px;
            display: inline-block;
        }
        
        /* Статический логотип */
        .logo-static {
            position: relative;
            background: radial-gradient(circle, #1a1a2e 0%, #0f0f23 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .static-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #00f5ff, #ff00ff, #00ff00);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #000;
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.8);
        }
        
        /* Анимированный логотип */
        .logo-animated {
            position: relative;
            background: radial-gradient(circle, #1a1a2e 0%, #0f0f23 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .animated-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #00f5ff, #ff00ff, #00ff00);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #000;
            animation: rotate 4s linear infinite, pulse 2s ease-in-out infinite alternate;
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.8);
        }
        
        .ring {
            position: absolute;
            border: 2px solid;
            border-radius: 50%;
            animation: ringRotate 6s linear infinite;
        }
        
        .ring-1 {
            width: 80px;
            height: 80px;
            border-color: rgba(0, 245, 255, 0.6);
            animation-duration: 3s;
        }
        
        .ring-2 {
            width: 100px;
            height: 100px;
            border-color: rgba(255, 0, 255, 0.4);
            animation-duration: 4s;
            animation-direction: reverse;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            100% { transform: scale(1.1); }
        }
        
        @keyframes ringRotate {
            0% { transform: rotate(0deg) scale(1); opacity: 0.8; }
            50% { transform: rotate(180deg) scale(1.05); opacity: 0.4; }
            100% { transform: rotate(360deg) scale(1); opacity: 0.8; }
        }
        
        .download-btn {
            background: #00f5ff;
            color: #000;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1 style="color: white;">Admins.Today Logo</h1>
    
    <div class="logo-static" id="static">
        <div class="static-icon">⚙️</div>
    </div>
    
    <div class="logo-animated" id="animated">
        <div class="ring ring-1"></div>
        <div class="ring ring-2"></div>
        <div class="animated-icon">⚙️</div>
    </div>
    
    <br>
    <button class="download-btn" onclick="downloadStatic()">Download PNG</button>
    <button class="download-btn" onclick="downloadAnimated()">Download GIF</button>
    
    <script>
        function downloadStatic() {
            const element = document.getElementById('static');
            html2canvas(element).then(canvas => {
                const link = document.createElement('a');
                link.download = 'admins-today-logo.png';
                link.href = canvas.toDataURL();
                link.click();
            });
        }
        
        function downloadAnimated() {
            alert('Right-click on animated logo and save as GIF');
        }
    </script>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</body>
</html>
