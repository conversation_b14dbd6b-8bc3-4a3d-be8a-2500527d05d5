// Admins.Today Website JavaScript

// Suppress browser extension errors
(function() {
    'use strict';

    // Handle unhandled promise rejections from extensions
    window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.stack &&
            (event.reason.stack.includes('content.bundle.js') ||
             event.reason.stack.includes('extension') ||
             event.reason.message.includes('browser is not defined'))) {
            event.preventDefault();
            console.warn('🔇 Suppressed browser extension error:', event.reason.message);
        }
    });

    // Handle regular errors from extensions
    window.addEventListener('error', function(event) {
        if (event.filename &&
            (event.filename.includes('content.bundle.js') ||
             event.filename.includes('extension') ||
             event.message.includes('browser is not defined'))) {
            event.preventDefault();
            console.warn('🔇 Suppressed browser extension error:', event.message);
            return true;
        }
    });
})();

// Knowledge Base for Chatbot
const knowledgeBase = {
    "послуги": {
        keywords: ["послуги", "сервіс", "що робите", "пропонуєте", "услуги"],
        response: "Ми надаємо наступні послуги:\n• Техпідтримка 24/7\n• Системне адміністрування\n• Хмарні рішення\n• Аудит безпеки\n• Налаштування серверів\n• Моніторинг інфраструктури"
    },
    "ціни": {
        keywords: ["ціна", "вартість", "тариф", "скільки коштує", "бюджет", "цена", "стоимость"],
        response: "Наші тарифи:\n• Техпідтримка: від $25/год\n• Системне адміністрування: від $35/год\n• Консультації: від $20/год\n• Проектні роботи: за домовленістю\n\nТочна вартість залежить від складності завдань."
    },
    "час": {
        keywords: ["час", "коли", "графік", "24/7", "цілодобово", "время", "когда"],
        response: "Ми працюємо 24/7 без вихідних. Час відповіді:\n• Критичні завдання: до 15 хвилин\n• Звичайні завдання: до 2 годин\n• Планові роботи: узгоджуємо заздалегідь"
    },
    "досвід": {
        keywords: ["досвід", "команда", "спеціалісти", "кваліфікація", "опыт"],
        response: "Наша команда:\n• 50+ сертифікованих спеціалістів\n• Середній досвід роботи: 7+ років\n• Сертифікації: AWS, Microsoft, Linux, Cisco\n• Обслуговуємо 200+ клієнтів"
    },
    "контакти": {
        keywords: ["контакт", "зв'язок", "телефон", "email", "написати", "связь"],
        response: "Зв'язатися з Admins.Today:\n📧 Email: <EMAIL>\n📱 Telegram: @admins_today\n💬 Чат на сайті (зараз активний)\n⏰ Працюємо 24/7"
    },
    "безопасность": {
        keywords: ["безопасность", "защита", "ssl", "firewall", "аудит"],
        response: "Услуги по безопасности:\n• Аудит безопасности серверов\n• Настройка firewall и защиты\n• Установка SSL сертификатов\n• Мониторинг угроз\n• Резервное копирование\n• Восстановление после инцидентов"
    },
    "облако": {
        keywords: ["облако", "cloud", "aws", "azure", "миграция"],
        response: "Облачные решения:\n• Миграция в облако (AWS, Azure, GCP)\n• Настройка Kubernetes\n• DevOps автоматизация\n• Мониторинг облачной инфраструктуры\n• Оптимизация затрат на облако"
    }
};

// DOM Elements
const chatbot = document.getElementById('chatbot');
const chatbotBody = document.getElementById('chatbotBody');
const chatMessages = document.getElementById('chatMessages');
const chatInput = document.getElementById('chatInput');
const chatToggle = document.getElementById('chatToggle');
const orderForm = document.getElementById('orderForm');

// Initialize with error handling
document.addEventListener('DOMContentLoaded', function() {
    try {
        initializeAnimations();
        initializeNavigation();
        initializeChatbot();
        initializeOrderForm();
        initializeHeroBackground();
        initializePersonalization();

        // Исправляем стили селекторов на всех страницах
        setTimeout(() => fixSelectStyles(), 500);

        // Suppress external extension errors
        window.addEventListener('error', function(e) {
            if (e.filename && e.filename.includes('content.bundle.js')) {
                e.preventDefault();
                return true;
            }
        });

        console.log('✅ Admins.Today website initialized successfully');
    } catch (error) {
        console.error('❌ Error initializing website:', error);
    }
});

// Navigation Functions
function initializeNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelector('.nav-links');

    if (hamburger && (navMenu || navLinks)) {
        hamburger.addEventListener('click', () => {
            if (navMenu) navMenu.classList.toggle('active');
            if (navLinks) navLinks.classList.toggle('active');
        });
    }

    // Smooth scrolling for navigation links
    document.querySelectorAll('.nav-link, .nav-links a').forEach(link => {
        link.addEventListener('click', (e) => {
            const href = link.getAttribute('href');
            if (href && href.startsWith('#')) {
                e.preventDefault();
                const targetId = href.substring(1);
                scrollToSection(targetId);
            }
        });
    });
    
    // Navbar scroll effect
    window.addEventListener('scroll', () => {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 50) {
            navbar.style.background = 'rgba(15, 15, 35, 0.95)';
        } else {
            navbar.style.background = 'rgba(15, 15, 35, 0.9)';
        }
    });
}

function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        const offsetTop = element.offsetTop - 70; // Account for fixed navbar
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
    }
}

// Animation Functions
function initializeAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.service-card, .info-card, .order-form').forEach(el => {
        observer.observe(el);
    });
}

// Chatbot Functions
function initializeChatbot() {
    if (!chatInput || !chatbot) {
        console.log('ℹ️ Chatbot elements not found on this page');
        return;
    }

    chatInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });

    // Initially collapsed
    chatbot.classList.add('collapsed');
}

function toggleChat() {
    if (!chatbot || !chatToggle) return;

    chatbot.classList.toggle('collapsed');
    const isCollapsed = chatbot.classList.contains('collapsed');
    chatToggle.style.transform = isCollapsed ? 'rotate(0deg)' : 'rotate(180deg)';
}

function sendMessage() {
    if (!chatInput || !chatMessages) return;

    const message = chatInput.value.trim();
    if (!message) return;
    
    // Add user message
    addMessage(message, 'user');
    chatInput.value = '';
    
    // Generate bot response
    setTimeout(() => {
        const response = generateBotResponse(message);
        addMessage(response, 'bot');
    }, 500);
}

function addMessage(content, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    
    const icon = document.createElement('i');
    icon.className = sender === 'bot' ? 'fas fa-robot' : 'fas fa-user';
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.textContent = content;
    
    messageDiv.appendChild(icon);
    messageDiv.appendChild(messageContent);
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function generateBotResponse(userMessage) {
    const message = userMessage.toLowerCase();
    
    // Find matching response from knowledge base
    for (const [key, data] of Object.entries(knowledgeBase)) {
        if (data.keywords.some(keyword => message.includes(keyword))) {
            return data.response;
        }
    }
    
    // Default responses
    const defaultResponses = [
        "Дякуємо за ваше питання! Для отримання детальної консультації, будь ласка, заповніть форму замовлення або зв'яжіться з нами через email або Telegram.",
        "Я можу допомогти з інформацією про наші послуги системного адміністрування, ціни, час роботи та контакти. Що саме вас цікавить?",
        "Для вирішення специфічних технічних питань рекомендую зв'язатися з нашими системними адміністраторами безпосередньо через форму замовлення."
    ];
    
    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
}

// Order Form Functions
function initializeOrderForm() {
    if (!orderForm) {
        console.log('ℹ️ Order form not found on this page');
        return;
    }

    // Принудительно исправляем стили селекторов
    fixSelectStyles();

    orderForm.addEventListener('submit', handleOrderSubmit);
    
    // Form validation
    const inputs = orderForm.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', (e) => clearFieldError(e.target));
    });

    // Обработчик выбора тарифа
    const tariffSelect = document.getElementById('tariff');
    const priceDisplay = document.getElementById('price-display');
    const selectedPrice = document.getElementById('selected-price');
    const payButton = document.getElementById('payButton');

    if (tariffSelect) {
        tariffSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const price = selectedOption.getAttribute('data-price');

            if (price) {
                selectedPrice.textContent = `$${price}`;
                priceDisplay.style.display = 'block';
                payButton.disabled = false;
                payButton.innerHTML = '<i class="fas fa-credit-card"></i> Оплатити $' + price;
            } else {
                priceDisplay.style.display = 'none';
                payButton.disabled = true;
                payButton.innerHTML = '<i class="fas fa-credit-card"></i> Оплатити і замовити';
            }
        });
    }
}

function handleOrderSubmit(e) {
    e.preventDefault();
    e.stopPropagation();

    console.log('🚀 Form submit intercepted - preventing default behavior');

    if (!validateForm()) {
        return false;
    }

    // Проверяем выбран ли тариф
    const tariffSelectElement = document.getElementById('tariff');
    if (!tariffSelectElement || !tariffSelectElement.value) {
        showError('Будь ласка, оберіть тариф для оплати');
        return false;
    }
    
    // Collect form data с тарифом
    const formData = new FormData(orderForm);
    const orderData = Object.fromEntries(formData.entries());

    // Добавляем цену из выбранного тарифа
    const selectedOption = tariffSelectElement.options[tariffSelectElement.selectedIndex];
    orderData.price = selectedOption.getAttribute('data-price');
    orderData.tariffName = selectedOption.textContent;

    // Show loading state
    const submitBtn = orderForm.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Підготовка оплати...';
    submitBtn.disabled = true;

    // Запускаем процесс оплаты
    initiatePayment(orderData, submitBtn, originalText);
    return false; // Предотвращаем стандартную отправку формы
}

// Функция инициации оплаты через WayForPay (БЕЗОПАСНАЯ версия)
async function initiatePayment(orderData, submitBtn, originalText) {
    try {
        console.log('💳 Отправляем данные на сервер для создания платежа...');

        // Отправляем данные на наш защищенный API
        const response = await fetch('/api/payment-api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(orderData)
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `Server error: ${response.status}`);
        }

        const paymentData = await response.json();
        console.log('✅ Получены данные для оплаты от сервера');

        // Проверяем доступность WayForPay SDK
        if (typeof Wayforpay === 'undefined') {
            throw new Error('WayForPay SDK не загружен');
        }

        // Запускаем виджет оплаты с подписанными данными от сервера
        const wayforpay = new Wayforpay();
        wayforpay.run(paymentData,
            // Success callback
            function(response) {
                console.log('✅ Оплата успешна:', response);
                handlePaymentSuccess(orderData, response, submitBtn, originalText);
            },
            // Failure callback
            function(response) {
                console.log('❌ Ошибка оплаты:', response);
                handlePaymentError(orderData, response, submitBtn, originalText);
            }
        );

    } catch (error) {
        console.error('❌ Ошибка инициализации оплаты:', error);
        handlePaymentError(orderData, {reasonText: error.message}, submitBtn, originalText);
    }
}

// Обработка успешной оплаты
function handlePaymentSuccess(orderData, paymentResponse, submitBtn, originalText) {
    // Добавляем данные об оплате
    orderData.payment = {
        status: 'paid',
        transactionId: paymentResponse.transactionId,
        amount: paymentResponse.amount,
        currency: paymentResponse.currency,
        timestamp: new Date().toISOString()
    };

    submitBtn.innerHTML = '<i class="fas fa-check"></i> Оплачено! Відправляємо...';

    // Отправляем данные в Telegram с информацией об оплате
    Promise.allSettled([
        saveToLocalStorage(orderData),
        sendToTelegram(orderData)
    ]).then(results => {
        const localStorage_result = results[0];
        const telegram_result = results[1];

        console.log('📊 Submission results after payment:', {
            localStorage: localStorage_result.status,
            telegram: telegram_result.status,
            payment: 'success'
        });

        // Показываем успех
        showSuccessMessage();
        document.getElementById('orderForm').reset();
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        // Track conversion с данными об оплате
        trackConversion(orderData);

        // Логируем ошибки
        if (localStorage_result.status === 'rejected') {
            console.error('❌ LocalStorage failed:', localStorage_result.reason);
        }
        if (telegram_result.status === 'rejected') {
            console.error('❌ Telegram failed:', telegram_result.reason);
        }
    });
}

// Обработка ошибки оплаты
function handlePaymentError(orderData, paymentResponse, submitBtn, originalText) {
    console.error('💳 Payment failed:', paymentResponse);

    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;

    showError(paymentResponse.reasonText || 'Помилка оплати. Спробуйте ще раз.');
}

// Функция показа ошибки
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--error-color);
        color: white;
        padding: 16px 20px;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        animation: slideInRight 0.3s ease-out;
    `;
    errorDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        ${message}
    `;

    document.body.appendChild(errorDiv);

    setTimeout(() => {
        errorDiv.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => errorDiv.remove(), 300);
    }, 5000);
}

// ✅ Функция generateSignature УДАЛЕНА!
// Теперь подпись генерируется БЕЗОПАСНО на сервере в payment-api.php

// Функция исправления стилей селекторов
function fixSelectStyles() {
    const selects = document.querySelectorAll('select');

    selects.forEach(select => {
        // Определяем тему пользователя
        const isDarkTheme = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

        if (isDarkTheme) {
            // Темная тема
            select.style.backgroundColor = '#1f2937';
            select.style.color = '#f9fafb';
            select.style.borderColor = '#374151';
        } else {
            // Светлая тема
            select.style.backgroundColor = '#ffffff';
            select.style.color = '#1a1a1a';
            select.style.borderColor = '#d1d5db';
        }

        // Исправляем стили опций
        const options = select.querySelectorAll('option');
        options.forEach(option => {
            if (isDarkTheme) {
                option.style.backgroundColor = '#1f2937';
                option.style.color = '#f9fafb';
            } else {
                option.style.backgroundColor = '#ffffff';
                option.style.color = '#1a1a1a';
            }
        });

        // Добавляем обработчик изменения темы
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addListener(() => {
                setTimeout(() => fixSelectStyles(), 100);
            });
        }
    });

    console.log('🎨 Select styles fixed for', selects.length, 'selectors');
}

function validateForm() {
    let isValid = true;
    const requiredFields = orderForm.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'Это поле обязательно для заполнения');
            isValid = false;
        }
    });
    
    // Email validation
    const emailField = orderForm.querySelector('#email');
    if (emailField.value && !isValidEmail(emailField.value)) {
        showFieldError(emailField, 'Введите корректный email адрес');
        isValid = false;
    }
    
    return isValid;
}

function validateField(e) {
    const field = e.target;
    clearFieldError(field);
    
    if (field.hasAttribute('required') && !field.value.trim()) {
        showFieldError(field, 'Это поле обязательно для заполнения');
        return false;
    }
    
    if (field.type === 'email' && field.value && !isValidEmail(field.value)) {
        showFieldError(field, 'Введите корректный email адрес');
        return false;
    }
    
    return true;
}

function showFieldError(field, message) {
    clearFieldError(field);
    
    field.style.borderColor = 'var(--error-color)';
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.color = 'var(--error-color)';
    errorDiv.style.fontSize = '0.875rem';
    errorDiv.style.marginTop = '4px';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

function clearFieldError(field) {
    field.style.borderColor = '';
    const errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Telegram Bot Configuration
const TELEGRAM_CONFIG = {
    botToken: '**********:AAF9vcNGYNLVxF6rKxT0dLolm1RpUuA_Kag',
    chatId: '-*************',
    apiUrl: 'https://api.telegram.org/bot'
};

// WayForPay Configuration (ТОЛЬКО публичные данные!)
const WAYFORPAY_CONFIG = {
    merchantDomainName: 'admins.today'
    // НЕТ secretKey - он на сервере!
    // НЕТ merchantAccount - он на сервере!
};

async function sendToTelegram(orderData) {
    // Проверяем, настроен ли Telegram бот (пока используем тестовые токены)
    console.log('🤖 Отправляем в Telegram:', {
        botToken: TELEGRAM_CONFIG.botToken.substring(0, 10) + '...',
        chatId: TELEGRAM_CONFIG.chatId,
        orderData: orderData
    });

    const message = formatTelegramMessage(orderData);
    const url = `${TELEGRAM_CONFIG.apiUrl}${TELEGRAM_CONFIG.botToken}/sendMessage`;

    const payload = {
        chat_id: TELEGRAM_CONFIG.chatId,
        text: message,
        parse_mode: 'HTML',
        disable_web_page_preview: true
    };

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Telegram API error response:', errorText);
            throw new Error(`Telegram API error: ${response.status} - ${errorText}`);
        }

        const result = await response.json();
        console.log('✅ Message sent to Telegram:', result);
        return result;

    } catch (error) {
        console.error('❌ Error sending to Telegram:', error);
        // Не бросаем ошибку, чтобы форма все равно работала
        return Promise.resolve({ ok: false, error: error.message });
    }
}

function formatTelegramMessage(orderData) {
    const urgencyEmoji = {
        'low': '🟢',
        'medium': '🟡',
        'high': '🟠',
        'critical': '🔴'
    };

    const serviceEmoji = {
        'support': '🎧',
        'admin': '🖥️',
        'security': '🛡️',
        'cloud': '☁️',
        'other': '⚙️'
    };

    const urgency = urgencyEmoji[orderData.urgency] || '⚪';
    const service = serviceEmoji[orderData.service] || '📋';

    const paymentInfo = orderData.payment ? `
💳 <b>ОПЛАЧЕНО!</b> $${orderData.payment.amount} ${orderData.payment.currency}
🆔 <b>ID транзакції:</b> ${orderData.payment.transactionId}
` : '';

    return `
🚨 <b>Нова заявка з Admins.Today!</b>
${paymentInfo}
👤 <b>Клієнт:</b> ${orderData.name}
📧 <b>Email:</b> ${orderData.email}

${service} <b>Послуга:</b> ${getServiceName(orderData.service)}
${urgency} <b>Терміновість:</b> ${getUrgencyName(orderData.urgency)}
${orderData.tariff ? `📦 <b>Тариф:</b> ${orderData.tariffName}\n` : ''}
${orderData.price ? `💰 <b>Сума:</b> $${orderData.price}/місяць\n` : ''}

📝 <b>Опис завдання:</b>
${orderData.description}

⏰ <b>Час подачі:</b> ${new Date().toLocaleString('uk-UA')}
🌐 <b>Джерело:</b> admins.today

#новаЗаявка #${orderData.service} #${orderData.urgency} ${orderData.payment ? '#оплачено' : '#безОплати'}
    `.trim();
}

function getServiceName(service) {
    const services = {
        'support': 'Техпідтримка 24/7',
        'admin': 'Системне адміністрування',
        'security': 'Безпека',
        'cloud': 'Хмарні рішення',
        'other': 'Інше'
    };
    return services[service] || service;
}

function getUrgencyName(urgency) {
    const urgencies = {
        'low': 'Низька (протягом дня)',
        'medium': 'Середня (протягом 4 годин)',
        'high': 'Висока (протягом години)',
        'critical': 'Критична (негайно)'
    };
    return urgencies[urgency] || urgency;
}

// ФУНКЦИЯ УДАЛЕНА - НАХУЙ ПОЧТУ! ТОЛЬКО TELEGRAM!

function saveToLocalStorage(orderData) {
    try {
        const orders = JSON.parse(localStorage.getItem('orders') || '[]');
        orders.push({
            ...orderData,
            timestamp: new Date().toISOString(),
            id: Date.now()
        });
        localStorage.setItem('orders', JSON.stringify(orders));
        console.log('Order saved to localStorage:', orderData);
        return Promise.resolve();
    } catch (error) {
        console.error('Error saving to localStorage:', error);
        return Promise.reject(error);
    }
}

function trackConversion(orderData) {
    // Google Analytics / Facebook Pixel tracking
    if (typeof gtag !== 'undefined') {
        gtag('event', 'conversion', {
            'send_to': 'AW-CONVERSION_ID/CONVERSION_LABEL',
            'value': orderData.budget || 50,
            'currency': 'USD'
        });
    }

    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('track', 'Lead', {
            value: orderData.budget || 50,
            currency: 'USD',
            content_name: getServiceName(orderData.service)
        });
    }

    console.log('Conversion tracked:', orderData);
}

function showErrorWithFallback(orderData) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--error-color);
        color: white;
        padding: 20px;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        max-width: 400px;
        animation: slideInRight 0.3s ease-out;
    `;

    errorDiv.innerHTML = `
        <div style="margin-bottom: 15px;">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Помилка відправки заявки</strong>
        </div>
        <p style="margin-bottom: 15px; font-size: 0.9rem;">
            Не вдалося відправити заявку автоматично. Будь ласка, зв'яжіться з нами:
        </p>
        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
            <a href="mailto:<EMAIL>" style="color: white; text-decoration: none; background: rgba(255,255,255,0.2); padding: 8px 12px; border-radius: 4px; font-size: 0.8rem;">
                📧 Email
            </a>
            <a href="https://t.me/admins_today" style="color: white; text-decoration: none; background: rgba(255,255,255,0.2); padding: 8px 12px; border-radius: 4px; font-size: 0.8rem;">
                📱 Telegram
            </a>
        </div>
        <button onclick="this.parentElement.remove()" style="position: absolute; top: 10px; right: 10px; background: none; border: none; color: white; font-size: 1.2rem; cursor: pointer;">×</button>
    `;

    document.body.appendChild(errorDiv);

    setTimeout(() => {
        if (errorDiv.parentElement) {
            errorDiv.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => errorDiv.remove(), 300);
        }
    }, 10000);
}

// ФУНКЦИЯ УДАЛЕНА - НАХУЙ ПОЧТУ!

function showSuccessMessage() {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--success-color);
        color: white;
        padding: 16px 20px;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        animation: slideInRight 0.3s ease-out;
    `;
    successDiv.innerHTML = `
        <i class="fas fa-check-circle"></i>
        Заявку надіслано! Наші адміни зв'яжуться з вами протягом 15 хвилин.
    `;

    document.body.appendChild(successDiv);

    setTimeout(() => {
        successDiv.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => successDiv.remove(), 300);
    }, 5000);
}

// Hero Background Animation Functions
function initializeHeroBackground() {
    createDynamicElements();
    startPerformanceMonitoring();
    createNetworkActivity();
}

function createDynamicElements() {
    const heroBackground = document.querySelector('.hero-background');
    if (!heroBackground) return;

    // Create network packets (reduced frequency)
    setInterval(() => {
        createNetworkPacket(heroBackground);
    }, 8000);

    // Create system alerts (reduced frequency)
    setInterval(() => {
        createSystemAlert(heroBackground);
    }, 20000);
}

function createFloatingServer(container) {
    const server = document.createElement('div');
    server.className = 'server-node';
    server.innerHTML = '<i class="fas fa-hdd"></i>';

    const x = Math.random() * 80 + 10; // 10% to 90%
    const y = Math.random() * 60 + 20; // 20% to 80%

    server.style.left = x + '%';
    server.style.top = y + '%';
    server.style.animationDelay = '0s';
    server.style.opacity = '0';

    container.appendChild(server);

    // Fade in
    setTimeout(() => {
        server.style.transition = 'opacity 1s ease-in-out';
        server.style.opacity = '1';
    }, 100);

    // Remove after animation
    setTimeout(() => {
        server.style.opacity = '0';
        setTimeout(() => {
            if (server.parentNode) {
                server.parentNode.removeChild(server);
            }
        }, 1000);
    }, 15000);
}

function createNetworkPacket(container) {
    const packet = document.createElement('div');
    packet.className = 'network-packet';
    packet.style.cssText = `
        position: absolute;
        width: 8px;
        height: 8px;
        background: var(--accent-color);
        border-radius: 50%;
        box-shadow: 0 0 10px var(--accent-color);
        animation: packetMove 4s linear forwards;
    `;

    const startX = Math.random() * 100;
    const startY = Math.random() * 100;
    const endX = Math.random() * 100;
    const endY = Math.random() * 100;

    packet.style.left = startX + '%';
    packet.style.top = startY + '%';

    // Add keyframes for this specific packet
    const keyframes = `
        @keyframes packetMove {
            0% {
                transform: translate(0, 0);
                opacity: 1;
            }
            100% {
                transform: translate(${endX - startX}vw, ${endY - startY}vh);
                opacity: 0;
            }
        }
    `;

    const styleSheet = document.createElement('style');
    styleSheet.textContent = keyframes;
    document.head.appendChild(styleSheet);

    container.appendChild(packet);

    // Clean up
    setTimeout(() => {
        if (packet.parentNode) {
            packet.parentNode.removeChild(packet);
        }
        if (styleSheet.parentNode) {
            styleSheet.parentNode.removeChild(styleSheet);
        }
    }, 4000);
}

function createSystemAlert(container) {
    const alert = document.createElement('div');
    alert.className = 'system-alert';
    alert.style.cssText = `
        position: absolute;
        top: ${Math.random() * 80 + 10}%;
        left: ${Math.random() * 80 + 10}%;
        background: rgba(239, 68, 68, 0.2);
        border: 1px solid var(--error-color);
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 10px;
        color: var(--error-color);
        animation: alertPulse 3s ease-in-out forwards;
        pointer-events: none;
        backdrop-filter: blur(5px);
    `;

    const alerts = [
        'High CPU Usage',
        'Memory Warning',
        'Disk Space Low',
        'Network Timeout',
        'SSL Cert Expiring',
        'Backup Failed',
        'Service Restart'
    ];

    alert.textContent = alerts[Math.floor(Math.random() * alerts.length)];
    container.appendChild(alert);

    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 3000);
}

function startPerformanceMonitoring() {
    // Simulate real-time performance data
    const updateMetrics = () => {
        const cpuBars = document.querySelectorAll('.cpu-load::before');
        const memoryCircles = document.querySelectorAll('.memory-circle');

        // Update would happen here in a real implementation
        // For demo purposes, the CSS animations handle the visual updates
    };

    setInterval(updateMetrics, 2000);
}

function createNetworkActivity() {
    const heroBackground = document.querySelector('.hero-background');
    if (!heroBackground) return;

    setInterval(() => {
        // Create data transfer visualization (reduced frequency)
        const transfer = document.createElement('div');
        transfer.style.cssText = `
            position: absolute;
            width: 2px;
            height: 20px;
            background: linear-gradient(to bottom, var(--success-color), transparent);
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: dataTransfer 4s ease-out forwards;
            pointer-events: none;
        `;

        heroBackground.appendChild(transfer);

        setTimeout(() => {
            if (transfer.parentNode) {
                transfer.parentNode.removeChild(transfer);
            }
        }, 4000);
    }, 6000);
}

// Personalization Functions
function initializePersonalization() {
    checkForStoredName();
    detectUserInfo();
    setupNameCollection();
}

function checkForStoredName() {
    const storedName = localStorage.getItem('userName');
    const lastVisit = localStorage.getItem('lastVisit');
    const now = new Date().getTime();

    if (storedName && lastVisit) {
        const daysSinceVisit = (now - parseInt(lastVisit)) / (1000 * 60 * 60 * 24);

        if (daysSinceVisit < 30) { // Помним имя 30 дней
            updatePersonalizedContent(storedName);
            return;
        }
    }

    // Если имени нет или прошло много времени, показываем приветствие
    // setTimeout(showNameDialog, 3000); // ОТКЛЮЧЕНО - убираем попап
}

function detectUserInfo() {
    // Попытка определить информацию из URL параметров
    const urlParams = new URLSearchParams(window.location.search);
    const nameFromUrl = urlParams.get('name') || urlParams.get('user') || urlParams.get('utm_name');

    if (nameFromUrl) {
        const decodedName = decodeURIComponent(nameFromUrl);
        saveUserName(decodedName);
        updatePersonalizedContent(decodedName);
        return;
    }

    // Попытка определить по email домену (если есть в localStorage)
    const savedEmail = localStorage.getItem('userEmail');
    if (savedEmail) {
        const nameFromEmail = extractNameFromEmail(savedEmail);
        if (nameFromEmail) {
            updatePersonalizedContent(nameFromEmail);
        }
    }

    // Определение по часовому поясу и языку
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const language = navigator.language || navigator.userLanguage;

    // Сохраняем для аналитики
    localStorage.setItem('userTimezone', timezone);
    localStorage.setItem('userLanguage', language);

    // Определение региона по часовому поясу
    const region = getRegionFromTimezone(timezone);
    localStorage.setItem('userRegion', region);
}

function extractNameFromEmail(email) {
    // Извлекаем имя из email адреса
    const localPart = email.split('@')[0];

    // Убираем цифры и специальные символы
    let name = localPart.replace(/[0-9._-]/g, ' ').trim();

    // Капитализируем первую букву
    if (name.length > 2) {
        return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
    }

    return null;
}

function getRegionFromTimezone(timezone) {
    const regionMap = {
        'Europe/Kiev': 'Україна',
        'Europe/Moscow': 'Росія',
        'Europe/Warsaw': 'Польща',
        'Europe/Berlin': 'Німеччина',
        'America/New_York': 'США',
        'America/Los_Angeles': 'США',
        'Asia/Tokyo': 'Японія'
    };

    return regionMap[timezone] || 'Невідомо';
}

function showNameDialog() {
    // Проверяем, не показывали ли уже диалог в этой сессии
    if (sessionStorage.getItem('nameDialogShown')) return;

    const dialog = document.createElement('div');
    dialog.className = 'name-dialog';
    dialog.innerHTML = `
        <div class="name-dialog-content">
            <div class="name-dialog-header">
                <i class="fas fa-user-circle"></i>
                <h3>Привіт! 👋</h3>
            </div>
            <p>Як до вас звертатися? Це допоможе нам персоналізувати спілкування.</p>
            <div class="name-input-group">
                <input type="text" id="visitorName" placeholder="Ваше ім'я" maxlength="20">
                <button onclick="saveVisitorName()" class="btn btn-primary">
                    <i class="fas fa-check"></i>
                    Зберегти
                </button>
            </div>
            <div class="name-dialog-actions">
                <button onclick="skipNameDialog()" class="btn btn-secondary">
                    Пропустити
                </button>
            </div>
        </div>
        <div class="name-dialog-overlay" onclick="skipNameDialog()"></div>
    `;

    document.body.appendChild(dialog);
    sessionStorage.setItem('nameDialogShown', 'true');

    // Фокус на поле ввода
    setTimeout(() => {
        const input = document.getElementById('visitorName');
        if (input) input.focus();
    }, 100);

    // Enter для сохранения
    document.getElementById('visitorName').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            saveVisitorName();
        }
    });
}

function saveVisitorName() {
    const nameInput = document.getElementById('visitorName');
    const name = nameInput.value.trim();

    if (name.length >= 2) {
        saveUserName(name);
        updatePersonalizedContent(name);
        closeNameDialog();

        // Показываем благодарность
        showPersonalizedWelcome(name);
    } else {
        // Показываем ошибку
        nameInput.style.borderColor = 'var(--error-color)';
        nameInput.placeholder = 'Введіть принаймні 2 символи';
    }
}

function skipNameDialog() {
    closeNameDialog();
    sessionStorage.setItem('nameSkipped', 'true');
}

function closeNameDialog() {
    const dialog = document.querySelector('.name-dialog');
    if (dialog) {
        dialog.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => dialog.remove(), 300);
    }
}

function saveUserName(name) {
    localStorage.setItem('userName', name);
    localStorage.setItem('lastVisit', new Date().getTime().toString());
}

function updatePersonalizedContent(name) {
    // Обновляем приветствие в чат-боте
    const botMessage = document.querySelector('.bot-message .message-content');
    if (botMessage) {
        botMessage.innerHTML = `Привіт, ${name}! 👋 Я помічник Admins.Today. Розповім про наші послуги системного адміністрування. Що вас цікавить?`;
    }

    // Обновляем заголовок героической секции
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle && !heroTitle.dataset.personalized) {
        const currentText = heroTitle.innerHTML;
        heroTitle.innerHTML = currentText.replace('Системні адміністратори', `${name}, системні адміністратори`);
        heroTitle.dataset.personalized = 'true';
    }

    // Обновляем placeholder в форме
    const nameField = document.querySelector('#name');
    if (nameField && !nameField.value) {
        nameField.value = name;
    }
}

function showPersonalizedWelcome(name) {
    const welcome = document.createElement('div');
    welcome.className = 'personalized-welcome';
    welcome.innerHTML = `
        <div class="welcome-content">
            <i class="fas fa-heart"></i>
            <h4>Дякуємо, ${name}!</h4>
            <p>Тепер ми знаємо, як до вас звертатися 😊</p>
        </div>
    `;

    document.body.appendChild(welcome);

    setTimeout(() => {
        welcome.style.animation = 'slideOutRight 0.5s ease-in';
        setTimeout(() => welcome.remove(), 500);
    }, 3000);
}

function setupNameCollection() {
    // Собираем имя из формы заказа
    const orderForm = document.getElementById('orderForm');
    if (orderForm) {
        orderForm.addEventListener('submit', (e) => {
            const nameField = document.querySelector('#name');
            const emailField = document.querySelector('#email');

            if (nameField && nameField.value.trim()) {
                const name = nameField.value.trim().split(' ')[0]; // Берем только имя
                if (!localStorage.getItem('userName')) {
                    saveUserName(name);
                }
            }

            if (emailField && emailField.value.trim()) {
                localStorage.setItem('userEmail', emailField.value.trim());
            }
        });

        // Также отслеживаем изменения в полях в реальном времени
        const emailField = document.querySelector('#email');
        if (emailField) {
            emailField.addEventListener('blur', () => {
                if (emailField.value.trim() && !localStorage.getItem('userName')) {
                    const nameFromEmail = extractNameFromEmail(emailField.value.trim());
                    if (nameFromEmail) {
                        // Предлагаем использовать имя из email
                        suggestNameFromEmail(nameFromEmail);
                    }
                }
            });
        }
    }
}

function suggestNameFromEmail(suggestedName) {
    const nameField = document.querySelector('#name');
    if (nameField && !nameField.value.trim()) {
        nameField.value = suggestedName;
        nameField.style.background = 'rgba(99, 102, 241, 0.1)';
        nameField.style.transition = 'background 0.3s ease';

        // Показываем подсказку
        showNameSuggestion(suggestedName);

        setTimeout(() => {
            nameField.style.background = '';
        }, 2000);
    }
}

function showNameSuggestion(name) {
    const suggestion = document.createElement('div');
    suggestion.className = 'name-suggestion';
    suggestion.innerHTML = `
        <i class="fas fa-lightbulb"></i>
        Ми підставили ім'я "${name}" з вашого email. Можете змінити, якщо потрібно.
    `;

    const nameField = document.querySelector('#name');
    if (nameField && nameField.parentNode) {
        nameField.parentNode.appendChild(suggestion);

        setTimeout(() => {
            suggestion.style.opacity = '0';
            setTimeout(() => suggestion.remove(), 300);
        }, 4000);
    }
}

// Глобальные функции для диалога
window.saveVisitorName = saveVisitorName;
window.skipNameDialog = skipNameDialog;

// Add CSS for dynamic animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    @keyframes alertPulse {
        0% { opacity: 0; transform: scale(0.8); }
        20% { opacity: 1; transform: scale(1); }
        80% { opacity: 1; transform: scale(1); }
        100% { opacity: 0; transform: scale(0.8); }
    }
    @keyframes dataTransfer {
        0% { opacity: 1; transform: translateY(0); }
        100% { opacity: 0; transform: translateY(-50px); }
    }
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }

    /* Name Dialog Styles */
    .name-dialog {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: fadeIn 0.3s ease-out;
    }

    .name-dialog-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(5px);
    }

    .name-dialog-content {
        position: relative;
        background: var(--bg-glass);
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-lg);
        padding: 40px;
        max-width: 400px;
        width: 90%;
        text-align: center;
        box-shadow: var(--shadow-xl);
        animation: scaleIn 0.3s ease-out;
    }

    .name-dialog-header {
        margin-bottom: 20px;
    }

    .name-dialog-header i {
        font-size: 3rem;
        color: var(--primary-color);
        margin-bottom: 10px;
    }

    .name-dialog-header h3 {
        margin: 0;
        color: var(--text-primary);
    }

    .name-dialog-content p {
        color: var(--text-secondary);
        margin-bottom: 25px;
        line-height: 1.5;
    }

    .name-input-group {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .name-input-group input {
        flex: 1;
        padding: 12px 16px;
        background: var(--bg-card);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        color: var(--text-primary);
        font-size: 16px;
    }

    .name-input-group input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    .name-dialog-actions button {
        background: transparent;
        border: 1px solid var(--border-color);
        color: var(--text-secondary);
        padding: 8px 16px;
        border-radius: var(--border-radius);
        cursor: pointer;
        font-size: 14px;
    }

    .name-dialog-actions button:hover {
        background: var(--bg-card);
        color: var(--text-primary);
    }

    /* Personalized Welcome */
    .personalized-welcome {
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--gradient-primary);
        color: white;
        padding: 20px;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-lg);
        z-index: 9999;
        animation: slideInRight 0.5s ease-out;
        max-width: 300px;
    }

    .welcome-content {
        text-align: center;
    }

    .welcome-content i {
        font-size: 1.5rem;
        margin-bottom: 8px;
        color: #ff6b9d;
    }

    .welcome-content h4 {
        margin: 0 0 5px 0;
        font-size: 1.1rem;
    }

    .welcome-content p {
        margin: 0;
        font-size: 0.9rem;
        opacity: 0.9;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes scaleIn {
        from { transform: scale(0.9); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
    }

    /* Name Suggestion */
    .name-suggestion {
        background: var(--gradient-primary);
        color: white;
        padding: 8px 12px;
        border-radius: var(--border-radius);
        font-size: 0.8rem;
        margin-top: 5px;
        display: flex;
        align-items: center;
        gap: 8px;
        animation: slideInUp 0.3s ease-out;
        transition: opacity 0.3s ease;
    }

    .name-suggestion i {
        color: #ffd700;
    }

    @keyframes slideInUp {
        from { transform: translateY(10px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }
`;
document.head.appendChild(style);
