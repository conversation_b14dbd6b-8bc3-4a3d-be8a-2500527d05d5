# Nginx Configuration for Admins.Today

## 🚀 Quick Setup

### 1. Copy site configuration
```bash
sudo cp nginx-site.conf /etc/nginx/sites-available/admins.today
```

### 2. Enable the site
```bash
sudo ln -s /etc/nginx/sites-available/admins.today /etc/nginx/sites-enabled/
```

### 3. Test configuration
```bash
sudo nginx -t
```

### 4. Reload Nginx
```bash
sudo systemctl reload nginx
```

## 📁 File Structure

```
/var/www/admins.today/
├── index.html
├── techsupport.html
├── sysadmin.html
├── cloud.html
├── security.html
├── contacts.html
├── refund-policy.html
├── terms-of-service.html
├── privacy-policy.html
├── 404.html
├── 500.html
├── styles.css
├── script.js
└── assets/
```

## 🔧 URL Mapping

| Clean URL | File | Status |
|-----------|------|--------|
| `/` | `index.html` | ✅ |
| `/index` | `index.html` | ✅ |
| `/techsupport` | `techsupport.html` | ✅ |
| `/sysadmin` | `sysadmin.html` | ✅ |
| `/cloud` | `cloud.html` | ✅ |
| `/security` | `security.html` | ✅ |
| `/contacts` | `contacts.html` | ✅ |
| `/refund-policy` | `refund-policy.html` | ✅ |
| `/terms-of-service` | `terms-of-service.html` | ✅ |
| `/privacy-policy` | `privacy-policy.html` | ✅ |

## 🔄 Redirects

- `*.html` → clean URL (301 redirect)
- `trailing/slash/` → `trailing/slash` (301 redirect)

## 🛡️ Security Features

- **XSS Protection**: `X-XSS-Protection: 1; mode=block`
- **Frame Options**: `X-Frame-Options: SAMEORIGIN`
- **Content Type**: `X-Content-Type-Options: nosniff`
- **Referrer Policy**: `strict-origin-when-cross-origin`
- **Hidden files**: Denied access to `.htaccess`, `.git`, etc.
- **Backup files**: Denied access to `*.bak`, `*.old`, etc.
- **PHP files**: Return 404 (security)

## ⚡ Performance

- **Gzip compression** for text files
- **Static file caching** (1 year for images, CSS, JS)
- **Gzip static** for pre-compressed files

## 🔍 Testing

### Test clean URLs:
```bash
curl -I http://admins.today/techsupport
# Should return 200 OK

curl -I http://admins.today/techsupport.html
# Should return 301 redirect to /techsupport
```

### Test redirects:
```bash
curl -I http://admins.today/contacts/
# Should return 301 redirect to /contacts
```

## 🌐 HTTPS Setup (Optional)

1. **Get SSL certificate** (Let's Encrypt recommended):
```bash
sudo certbot --nginx -d admins.today -d www.admins.today
```

2. **Certbot will automatically update** the Nginx configuration

## 📊 Monitoring

### Check Nginx status:
```bash
sudo systemctl status nginx
```

### View access logs:
```bash
sudo tail -f /var/log/nginx/admins.today.access.log
```

### View error logs:
```bash
sudo tail -f /var/log/nginx/admins.today.error.log
```

## 🔧 Troubleshooting

### Common issues:

1. **404 errors**: Check file permissions
```bash
sudo chown -R www-data:www-data /var/www/admins.today
sudo chmod -R 755 /var/www/admins.today
```

2. **Configuration errors**: Test config
```bash
sudo nginx -t
```

3. **Clean URLs not working**: Check if rewrite module is enabled
```bash
nginx -V 2>&1 | grep -o with-http_rewrite_module
```

## 📝 Notes

- **Root directory**: `/var/www/admins.today`
- **User**: `www-data`
- **Permissions**: `755` for directories, `644` for files
- **Charset**: UTF-8
- **Index file**: `index.html`

## 🚀 Production Checklist

- [ ] SSL certificate installed
- [ ] HTTPS redirect enabled
- [ ] Security headers configured
- [ ] Gzip compression enabled
- [ ] Static file caching configured
- [ ] Error pages created (404.html, 500.html)
- [ ] Log rotation configured
- [ ] Firewall rules set
- [ ] Backup strategy in place
