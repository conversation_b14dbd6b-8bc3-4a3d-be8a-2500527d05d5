# 🔐 Безопасная настройка Admins.Today

## 📁 Структура проекта:

```
/var/www/admins.today/
├── index.html                     ✅ Публичный
├── styles.css                     ✅ Публичный  
├── script.js                      ✅ Публичный (БЕЗ секретов!)
├── techsupport.html               ✅ Публичный
├── sysadmin.html                  ✅ Публичный
├── cloud.html                     ✅ Публичный
├── security.html                  ✅ Публичный
├── contacts.html                  ✅ Публичный
├── refund-policy.html             ✅ Публичный
├── terms-of-service.html          ✅ Публичный
├── privacy-policy.html            ✅ Публичный
├── payment-success.html           ✅ Публичный (страница успеха)
├── payment-error.html             ✅ Публичный (страница ошибки)
├── api/                           📁 API папка
│   ├── config.php                 🔒 СЕКРЕТНЫЙ (защищен .htaccess)
│   ├── payment-api.php            ✅ Публичный API endpoint
│   ├── payment-callback.php       ✅ Публичный callback
│   ├── .htaccess                  🔒 Защита секретных файлов
│   └── orders/                    🔒 ЗАЩИЩЕНА (заказы)
│       ├── order_123.json         🔒 Данные заказов
│       └── order_124.json         🔒 Данные заказов
├── nginx-admins-today.conf        📋 Конфигурация nginx
└── SECURITY-SETUP.md              📖 Эта инструкция
```

## 🚀 Установка и настройка:

### 1. **Настройка секретных ключей:**

Отредактируйте `api/config.php`:

```php
'wayforpay' => [
    'merchant_account' => 'ВАШ_РЕАЛЬНЫЙ_MERCHANT_ACCOUNT',
    'secret_key' => 'ВАШ_РЕАЛЬНЫЙ_SECRET_KEY',
    'domain' => 'admins.today'
],
'telegram' => [
    'bot_token' => 'ВАШ_РЕАЛЬНЫЙ_BOT_TOKEN',
    'chat_id' => 'ВАШ_РЕАЛЬНЫЙ_CHAT_ID'
]
```

### 2. **Права доступа к файлам:**

```bash
# Секретные файлы - только владелец
chmod 600 api/config.php
chmod 700 api/orders/

# Публичные файлы
chmod 644 *.html *.css *.js
chmod 755 api/payment-api.php api/payment-callback.php

# Защита .htaccess
chmod 644 api/.htaccess
```

### 3. **Настройка nginx:**

```bash
# Копируем конфигурацию
sudo cp nginx-admins-today.conf /etc/nginx/sites-available/admins.today

# Активируем сайт
sudo ln -s /etc/nginx/sites-available/admins.today /etc/nginx/sites-enabled/

# Проверяем конфигурацию
sudo nginx -t

# Перезагружаем nginx
sudo systemctl reload nginx
```

### 4. **Настройка PHP-FPM:**

Убедитесь что PHP-FPM работает:

```bash
sudo systemctl status php8.1-fpm
sudo systemctl enable php8.1-fpm
```

### 5. **Создание папки для заказов:**

```bash
mkdir -p /var/www/admins.today/api/orders
chmod 700 /var/www/admins.today/api/orders
chown www-data:www-data /var/www/admins.today/api/orders
```

## 🔒 Что защищено:

### ✅ **Секретные данные на сервере:**
- `config.php` - недоступен извне
- `orders/` - папка с заказами защищена
- Secret keys - только на сервере

### ✅ **Rate limiting:**
- API: 5 запросов в минуту
- Callback: 10 запросов в минуту
- Burst protection

### ✅ **Валидация данных:**
- Проверка email
- Проверка цены
- Проверка подписи WayForPay

### ✅ **Security headers:**
- X-Frame-Options
- X-Content-Type-Options  
- X-XSS-Protection
- Content-Security-Policy

## 🧪 Тестирование:

### 1. **Проверка API:**

```bash
# Тест создания платежа
curl -X POST https://admins.today/api/payment-api.php \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>", 
    "tariff": "basic",
    "tariffName": "Базовый тариф",
    "price": "20"
  }'
```

### 2. **Проверка защиты:**

```bash
# Должно вернуть 404
curl https://admins.today/api/config.php
curl https://admins.today/api/orders/

# Должно работать
curl https://admins.today/api/payment-api.php
```

## 🚨 ВАЖНО для продакшена:

1. **SSL сертификат** - обязательно HTTPS
2. **Firewall** - закрыть ненужные порты  
3. **Backup** - регулярные бекапы заказов
4. **Мониторинг** - логи ошибок и атак
5. **Обновления** - регулярно обновлять PHP/nginx

## 📞 Поддержка:

При проблемах проверьте логи:
- `/var/log/nginx/admins.today.error.log`
- `/var/log/php8.1-fpm.log`
- PHP error_log

---

**🔐 Теперь ваш сайт БЕЗОПАСЕН для продакшена!**
