<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест чистых URL - Admins.Today</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div style="padding: 50px; background: #0f0f23; color: white; min-height: 100vh;">
        <h1 style="color: #00f5ff; text-align: center;">🎉 Тест чистых URL работает!</h1>
        
        <div style="max-width: 800px; margin: 0 auto; background: #1a1a2e; padding: 30px; border-radius: 10px; margin-top: 30px;">
            <h2 style="color: #ff00ff;">Проверьте эти URL:</h2>
            
            <div style="display: grid; gap: 15px; margin-top: 20px;">
                <div style="background: #333; padding: 15px; border-radius: 5px;">
                    <strong style="color: #00ff00;">✅ Должно работать:</strong><br>
                    <code style="color: #00f5ff;">site.tld/index</code> → index.html
                </div>
                
                <div style="background: #333; padding: 15px; border-radius: 5px;">
                    <strong style="color: #00ff00;">✅ Должно работать:</strong><br>
                    <code style="color: #00f5ff;">site.tld/techsupport</code> → techsupport.html
                </div>
                
                <div style="background: #333; padding: 15px; border-radius: 5px;">
                    <strong style="color: #00ff00;">✅ Должно работать:</strong><br>
                    <code style="color: #00f5ff;">site.tld/contacts</code> → contacts.html
                </div>
                
                <div style="background: #333; padding: 15px; border-radius: 5px;">
                    <strong style="color: #ffff00;">🔄 Должно редиректить:</strong><br>
                    <code style="color: #ff00ff;">site.tld/index.html</code> → site.tld/index
                </div>
            </div>
            
            <h3 style="color: #ff00ff; margin-top: 30px;">Тестовые ссылки:</h3>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-top: 15px;">
                <a href="/index" style="background: #00f5ff; color: #000; padding: 10px 15px; border-radius: 5px; text-decoration: none; font-weight: bold;">
                    /index
                </a>
                <a href="/techsupport" style="background: #ff00ff; color: #000; padding: 10px 15px; border-radius: 5px; text-decoration: none; font-weight: bold;">
                    /techsupport
                </a>
                <a href="/contacts" style="background: #00ff00; color: #000; padding: 10px 15px; border-radius: 5px; text-decoration: none; font-weight: bold;">
                    /contacts
                </a>
                <a href="/test-url" style="background: #ffff00; color: #000; padding: 10px 15px; border-radius: 5px; text-decoration: none; font-weight: bold;">
                    /test-url
                </a>
            </div>
            
            <div style="margin-top: 30px; padding: 20px; background: rgba(255, 0, 255, 0.1); border-radius: 5px; border: 1px solid #ff00ff;">
                <h4 style="color: #ff00ff; margin: 0 0 10px 0;">Как работает .htaccess:</h4>
                <ol style="color: #ccc; line-height: 1.6;">
                    <li><strong>Убирает .html</strong> из URL (редирект 301)</li>
                    <li><strong>Добавляет .html</strong> внутренне для обработки</li>
                    <li><strong>Специальные правила</strong> для каждой страницы</li>
                    <li><strong>Убирает trailing slash</strong> для файлов</li>
                </ol>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="/" style="background: linear-gradient(45deg, #00f5ff, #ff00ff); color: #000; padding: 15px 30px; border-radius: 25px; text-decoration: none; font-weight: bold; display: inline-block;">
                    🏠 Вернуться на главную
                </a>
            </div>
        </div>
    </div>
</body>
</html>
