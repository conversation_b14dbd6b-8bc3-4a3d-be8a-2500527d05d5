<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Помилка оплати - Admins.Today</title>
    <meta name="description" content="Виникла помилка при обробці оплати. Спробуйте ще раз або зв'яжіться з нами.">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">
    
    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            background: linear-gradient(135deg, 
                rgba(239, 68, 68, 0.1) 0%, 
                rgba(245, 101, 101, 0.1) 100%);
        }
        
        .error-container {
            max-width: 600px;
            width: 100%;
            background: var(--bg-secondary);
            border-radius: var(--border-radius-lg);
            padding: 3rem;
            text-align: center;
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--border-color);
        }
        
        .error-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            background: linear-gradient(135deg, var(--error-color), #dc2626);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            animation: errorShake 1s ease-in-out;
            box-shadow: 0 0 30px rgba(239, 68, 68, 0.3);
        }
        
        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .error-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--error-color), #dc2626);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .error-message {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .error-details {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: left;
        }
        
        .error-details h4 {
            color: var(--error-color);
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .error-details ul {
            list-style: none;
            padding: 0;
        }
        
        .error-details li {
            padding: 0.5rem 0;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .error-details li i {
            color: var(--error-color);
            width: 20px;
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .error-info {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: left;
        }
        
        .error-info h4 {
            color: var(--warning-color);
            margin-bottom: 1rem;
        }
        
        .error-info p {
            margin: 0.5rem 0;
            color: var(--text-secondary);
        }
        
        @media (max-width: 768px) {
            .error-container {
                padding: 2rem 1.5rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-logo">
                <div class="nav-logo-icon">
                    <img src="logo.svg" alt="Admins.Today Logo" />
                </div>
                <span>Admins.Today</span>
            </div>
            <div class="nav-links">
                <a href="/">Головна</a>
                <a href="/#services">Послуги</a>
                <a href="/contacts">Контакти</a>
            </div>
        </div>
    </nav>

    <!-- Error Page -->
    <main class="error-page">
        <div class="error-container">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <h1 class="error-title">Помилка оплати</h1>
            
            <p class="error-message">
                На жаль, виникла помилка при обробці вашої оплати. Не хвилюйтеся, кошти не списані з вашого рахунку.
            </p>
            
            <div class="error-info" id="errorInfo">
                <h4><i class="fas fa-info-circle"></i> Деталі помилки</h4>
                <p><strong>Код помилки:</strong> <span id="errorCode">Не вказано</span></p>
                <p><strong>Опис:</strong> <span id="errorDescription">Загальна помилка оплати</span></p>
                <p><strong>Час:</strong> <span id="errorTime"></span></p>
            </div>
            
            <div class="error-details">
                <h4><i class="fas fa-tools"></i> Що можна зробити?</h4>
                <ul>
                    <li><i class="fas fa-redo"></i> Спробуйте оплатити ще раз через кілька хвилин</li>
                    <li><i class="fas fa-credit-card"></i> Перевірте дані вашої картки</li>
                    <li><i class="fas fa-wifi"></i> Переконайтеся в стабільності інтернет-з'єднання</li>
                    <li><i class="fas fa-phone"></i> Зв'яжіться з нами для допомоги</li>
                </ul>
            </div>
            
            <div class="error-actions">
                <a href="/#order" class="btn btn-primary">
                    <i class="fas fa-redo"></i>
                    Спробувати ще раз
                </a>
                <a href="/contacts" class="btn btn-secondary">
                    <i class="fas fa-headset"></i>
                    Зв'язатися з підтримкою
                </a>
                <a href="https://t.me/admins_today" class="btn btn-outline">
                    <i class="fab fa-telegram"></i>
                    Telegram
                </a>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <div class="footer-futuristic-icon">
                            <div class="footer-icon-core">
                                <i class="fas fa-microchip"></i>
                            </div>
                            <div class="footer-icon-rings">
                                <div class="footer-ring footer-ring-1"></div>
                                <div class="footer-ring footer-ring-2"></div>
                            </div>
                        </div>
                        <span>Admins.Today</span>
                    </div>
                    <p>Професійні системні адміністратори та техпідтримка для вашого бізнесу</p>
                </div>
                <div class="footer-section">
                    <h4>Контакти</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>

                        <p><i class="fas fa-clock"></i> 24/7</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Admins.Today. Усі права захищені.</p>
            </div>
        </div>
    </footer>

    <script>
        // Получаем параметры из URL
        const urlParams = new URLSearchParams(window.location.search);
        
        // Заполняем информацию об ошибке
        document.getElementById('errorCode').textContent = 
            urlParams.get('reasonCode') || 'Не вказано';
        
        document.getElementById('errorDescription').textContent = 
            urlParams.get('reasonText') || 'Загальна помилка оплати';
        
        document.getElementById('errorTime').textContent = 
            new Date().toLocaleString('uk-UA');
        
        // Отправляем событие об ошибке в аналитику
        if (typeof gtag !== 'undefined') {
            gtag('event', 'payment_error', {
                'error_code': urlParams.get('reasonCode'),
                'error_description': urlParams.get('reasonText')
            });
        }
        
        console.log('❌ Payment error page loaded');
    </script>
</body>
</html>
